#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بديل Flask بدلاً من Gradio لتجنب مشاكل JSON Schema
Flask alternative instead of Gradio to avoid JSON Schema issues
"""

from flask import Flask, request, render_template_string, jsonify
import whisper
import os
import tempfile
import warnings

warnings.filterwarnings("ignore")

# إنشاء تطبيق Flask
app = Flask(__name__)

# متغيرات عامة
model = None
API_KEY = "whisper-flask-2025"

def load_whisper_model():
    """تحميل نموذج Whisper"""
    global model
    if model is None:
        try:
            # محاولة تحميل النموذج المحلي
            if os.path.exists("./models/small.pt"):
                model = whisper.load_model("./models/small.pt")
                print("✅ تم تحميل النموذج المحلي")
            else:
                # تحميل نموذج tiny (سريع)
                model = whisper.load_model("tiny")
                print("✅ تم تحميل نموذج tiny")
        except Exception as e:
            print(f"خطأ في تحميل النموذج: {e}")
            # محاولة أخيرة
            model = whisper.load_model("base")
            print("✅ تم تحميل نموذج base")
    return model

# HTML للواجهة
HTML_TEMPLATE = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محول الصوت إلى نص</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            background: #f8f9ff;
        }
        input[type="file"] {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100%;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin: 10px 0;
        }
        button:hover {
            background: #5a6fd8;
        }
        #result {
            margin-top: 20px;
            padding: 20px;
            background: #f0f0f0;
            border-radius: 8px;
            min-height: 100px;
            white-space: pre-wrap;
        }
        .api-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 محول الصوت إلى نص</h1>
        
        <form id="uploadForm" enctype="multipart/form-data">
            <div class="upload-area">
                <h3>📁 ارفع ملف صوتي</h3>
                <input type="file" name="audio" accept="audio/*" required>
                <p>يدعم: MP3, WAV, M4A, FLAC</p>
            </div>
            
            <button type="submit">🚀 تحويل إلى نص</button>
        </form>
        
        <div id="result">النتيجة ستظهر هنا...</div>
        
        <div class="api-info">
            <h4>🔑 معلومات API:</h4>
            <p><strong>API Key:</strong> {{ api_key }}</p>
            <p><strong>Endpoint:</strong> POST /transcribe</p>
        </div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            resultDiv.textContent = '⏳ جاري التحويل...';
            
            try {
                const response = await fetch('/transcribe', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.textContent = data.text;
                } else {
                    resultDiv.textContent = '❌ خطأ: ' + data.error;
                }
            } catch (error) {
                resultDiv.textContent = '❌ خطأ في الاتصال: ' + error.message;
            }
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template_string(HTML_TEMPLATE, api_key=API_KEY)

@app.route('/transcribe', methods=['POST'])
def transcribe():
    """تحويل الصوت إلى نص"""
    try:
        # التحقق من وجود الملف
        if 'audio' not in request.files:
            return jsonify({'success': False, 'error': 'لم يتم رفع ملف'})
        
        audio_file = request.files['audio']
        if audio_file.filename == '':
            return jsonify({'success': False, 'error': 'لم يتم اختيار ملف'})
        
        # حفظ الملف مؤقتاً
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
            audio_file.save(tmp_file.name)
            
            # تحميل النموذج
            whisper_model = load_whisper_model()
            
            # تحويل الصوت
            result = whisper_model.transcribe(tmp_file.name)
            text = result["text"].strip()
            
            # حذف الملف المؤقت
            os.unlink(tmp_file.name)
            
            if not text:
                return jsonify({'success': False, 'error': 'لم يتم العثور على نص'})
            
            # إضافة معلومات
            word_count = len(text.split())
            final_text = f"{text}\n\n📊 عدد الكلمات: {word_count}\n🔑 API Key: {API_KEY}"
            
            return jsonify({'success': True, 'text': final_text})
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/transcribe', methods=['POST'])
def api_transcribe():
    """API endpoint للاستخدام البرمجي"""
    return transcribe()

if __name__ == '__main__':
    print("🚀 بدء تطبيق Flask...")
    print(f"🔑 API Key: {API_KEY}")
    
    # تشغيل التطبيق
    app.run(host='0.0.0.0', port=7860, debug=False)
