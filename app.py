import gradio as gr
import whisper
import os

# API Key ثابت للاستخدام الشخصي
API_KEY = "whisper-personal-2025"

# متغير النموذج
model = None

def get_model():
    """تحميل النموذج عند الحاجة فقط (lazy loading)"""
    global model

    if model is not None:
        return model

    # مسار النموذج المحلي
    model_path = "./models/small.pt"

    print(f"🔍 تحميل النموذج من: {model_path}")

    # التحقق من وجود النموذج
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"النموذج المحلي غير موجود في: {model_path}")

    # تحميل النموذج
    try:
        model = whisper.load_model(model_path)
        file_size = os.path.getsize(model_path)
        size_mb = file_size / (1024 * 1024)
        print(f"✅ تم تحميل نموذج small محلي ({size_mb:.1f}MB)")
        return model
    except Exception as e:
        raise Exception(f"خطأ في تحميل النموذج: {str(e)}")

def check_model_status():
    """فحص حالة النموذج بدون تحميله"""
    model_path = "./models/small.pt"

    if not os.path.exists(model_path):
        return "❌ النموذج المحلي غير موجود"

    file_size = os.path.getsize(model_path)
    size_mb = file_size / (1024 * 1024)
    return f"✅ نموذج small محلي جاهز ({size_mb:.1f}MB)"

def transcribe_audio(audio_file):
    """تحويل الصوت إلى نص باستخدام النموذج المحلي"""
    if audio_file is None:
        return "❌ يرجى رفع ملف صوتي"

    try:
        # تحميل النموذج عند الحاجة فقط
        current_model = get_model()

        print(f"🎤 تحويل الملف: {audio_file}")
        result = current_model.transcribe(audio_file)
        text = result["text"].strip()
        print(f"✅ تم التحويل بنجاح - طول النص: {len(text)} حرف")
        return text

    except FileNotFoundError as e:
        error_msg = f"❌ {str(e)}"
        print(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"❌ خطأ في التحويل: {str(e)}"
        print(error_msg)
        return error_msg

# فحص حالة النموذج بدون تحميله
print("🚀 بدء تشغيل التطبيق...")
model_status = check_model_status()
print(model_status)

# إنشاء الواجهة
with gr.Blocks(title="محول الصوت - نموذج محلي") as app:

    # العنوان
    gr.HTML("""
    <div style="text-align: center; padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; margin: 10px;">
        <h2>🎤 محول الصوت إلى نص</h2>
        <p>نموذج Small محلي - دقة عالية</p>
    </div>
    """)

    # معلومات النموذج والAPI
    with gr.Row():
        with gr.Column():
            gr.HTML(f"""
            <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px; border: 1px solid #c3e6cb;">
                <h4 style="color: #155724; margin: 0;">🔑 API Key:</h4>
                <code style="background: white; padding: 8px; border-radius: 4px; display: block; margin: 5px 0; word-break: break-all; font-size: 14px;">
                    {API_KEY}
                </code>
            </div>
            """)

        with gr.Column():
            gr.HTML(f"""
            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px; border: 1px solid #ffeaa7;">
                <h4 style="color: #856404; margin: 0;">🤖 حالة النموذج:</h4>
                <p style="background: white; padding: 8px; border-radius: 4px; margin: 5px 0; font-size: 14px;">
                    {model_status}
                </p>
            </div>
            """)

    # واجهة التحويل
    with gr.Row():
        audio_input = gr.Audio(
            label="📁 ارفع ملف صوتي",
            type="filepath"
        )
        output_text = gr.Textbox(
            label="📝 النص",
            lines=8
        )

    convert_btn = gr.Button("🚀 تحويل", variant="primary")

    # معلومات API
    gr.HTML(f"""
    <div style="background: #fafafa; padding: 15px; border-radius: 5px; margin: 10px 0;">
        <h4>📡 استخدام API:</h4>
        <pre style="background: #333; color: #fff; padding: 10px; border-radius: 3px; font-size: 12px;">
import requests
import json

API_URL = "https://YOUR-SPACE-URL.hf.space/api/predict"

with open("audio.mp3", "rb") as f:
    files = {{"data": f}}
    data = {{"data": [None]}}

response = requests.post(API_URL, files=files, data=json.dumps(data))
result = response.json()
print(result["data"][0])
        </pre>
        <p><strong>API Key:</strong> {API_KEY}</p>
    </div>
    """)

    # ربط الأحداث
    convert_btn.click(
        fn=transcribe_audio,
        inputs=[audio_input],
        outputs=[output_text]
    )

# تشغيل التطبيق - يعمل في كل البيئات
print("✅ التطبيق جاهز للاستخدام")
print(f"🔑 API Key: {API_KEY}")

# تشغيل مع إعدادات مناسبة لـ Hugging Face Spaces
if __name__ == "__main__":
    # للاستخدام المحلي
    try:
        app.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False,
            debug=False,
            show_error=True,
            quiet=False
        )
    except Exception as e:
        print(f"❌ خطأ في التشغيل المحلي: {e}")
        # محاولة بديلة للتشغيل المحلي
        app.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=False
        )
else:
    # للاستخدام في Hugging Face Spaces
    app.launch(share=True)
