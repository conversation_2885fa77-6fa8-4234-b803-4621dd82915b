#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة جداً تتجنب جميع مشاكل Gradio
Ultra-minimal version that avoids all Gradio issues
"""

import gradio as gr
import whisper
import os
import warnings

# إيقاف جميع التحذيرات
warnings.filterwarnings("ignore")

# متغيرات عامة
model = None
API_KEY = "whisper-minimal-2025"

def get_model():
    """تحميل النموذج بأبسط طريقة"""
    global model
    if model is None:
        try:
            # محاولة تحميل النموذج المحلي
            if os.path.exists("./models/small.pt"):
                model = whisper.load_model("./models/small.pt")
                print("✅ تم تحميل النموذج المحلي")
            else:
                # تحميل أصغر نموذج ممكن
                model = whisper.load_model("tiny")
                print("✅ تم تحميل نموذج tiny")
        except Exception as e:
            print(f"خطأ في تحميل النموذج: {e}")
            # محاولة أخيرة مع base
            model = whisper.load_model("base")
            print("✅ تم تحميل نموذج base")
    return model

def convert_audio(audio_file):
    """تحويل الصوت إلى نص - مبسط جداً"""
    if audio_file is None:
        return "❌ يرجى رفع ملف صوتي"
    
    try:
        # تحميل النموذج
        whisper_model = get_model()
        
        # تحويل الصوت
        result = whisper_model.transcribe(audio_file)
        text = result["text"].strip()
        
        if not text:
            return "❌ لم يتم العثور على نص في الملف"
        
        # إضافة معلومات بسيطة
        word_count = len(text.split())
        return f"{text}\n\n📊 عدد الكلمات: {word_count}\n🔑 API Key: {API_KEY}"
        
    except Exception as e:
        return f"❌ خطأ في التحويل: {str(e)}"

# إنشاء التطبيق بأبسط طريقة ممكنة
print("🚀 بدء التطبيق المبسط...")

# استخدام أبسط شكل من Interface
app = gr.Interface(
    fn=convert_audio,
    inputs=gr.Audio(type="filepath"),
    outputs=gr.Textbox(lines=10),
    title="محول الصوت إلى نص",
    description="ارفع ملف صوتي للحصول على النص"
)

print("✅ التطبيق جاهز")
print(f"🔑 API Key: {API_KEY}")

# تشغيل مباشر وبسيط
app.launch(share=True)
