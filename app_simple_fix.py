#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة جداً لتجنب أخطاء Gradio في Hugging Face Spaces
Ultra-simple version to avoid Gradio errors in Hugging Face Spaces
"""

import gradio as gr
import whisper
import os
import warnings

# تجاهل جميع التحذيرات
warnings.filterwarnings("ignore")

# API Key ثابت
API_KEY = "whisper-hf-spaces-2025"

# متغير النموذج
model = None

def load_model():
    """تحميل النموذج بطريقة آمنة"""
    global model
    if model is not None:
        return model
    
    try:
        # محاولة تحميل النموذج المحلي أولاً
        if os.path.exists("./models/small.pt"):
            model = whisper.load_model("./models/small.pt")
            print("✅ تم تحميل النموذج المحلي")
        else:
            # تحميل نموذج صغير من الإنترنت
            model = whisper.load_model("base")
            print("✅ تم تحميل نموذج base")
        return model
    except Exception as e:
        print(f"خطأ في تحميل النموذج: {e}")
        # محاولة أخيرة مع أصغر نموذج
        model = whisper.load_model("tiny")
        print("✅ تم تحميل نموذج tiny")
        return model

def transcribe(audio_file):
    """تحويل الصوت إلى نص - مبسط"""
    if audio_file is None:
        return "❌ يرجى رفع ملف صوتي"
    
    try:
        # تحميل النموذج
        whisper_model = load_model()
        
        # تحويل الصوت
        result = whisper_model.transcribe(audio_file)
        text = result["text"].strip()
        
        if not text:
            return "❌ لم يتم العثور على نص في الملف الصوتي"
        
        return text
        
    except Exception as e:
        return f"❌ خطأ في التحويل: {str(e)}"

# إنشاء واجهة مبسطة جداً
print("🚀 بدء التطبيق...")

# استخدام Interface بدلاً من Blocks لتجنب مشاكل التكوين
demo = gr.Interface(
    fn=transcribe,
    inputs=gr.Audio(type="filepath", label="ارفع ملف صوتي"),
    outputs=gr.Textbox(label="النص المحول", lines=10),
    title="🎤 محول الصوت إلى نص",
    description="مدعوم بنموذج Whisper - ارفع ملف صوتي للحصول على النص",
    examples=None,
    allow_flagging="never"
)

# تشغيل التطبيق بأبسط طريقة ممكنة
print("✅ التطبيق جاهز")
print(f"🔑 API Key: {API_KEY}")

# تشغيل مباشر مع share=True
demo.launch(share=True, server_name="0.0.0.0", server_port=7860)
