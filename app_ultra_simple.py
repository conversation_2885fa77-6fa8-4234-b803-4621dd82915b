import gradio as gr
import whisper
import warnings

warnings.filterwarnings("ignore")

model = None

def load_model():
    global model
    if model is None:
        model = whisper.load_model("tiny")
    return model

def transcribe(audio):
    if audio is None:
        return "يرجى رفع ملف صوتي"
    try:
        m = load_model()
        result = m.transcribe(audio)
        return result["text"]
    except:
        return "خطأ في التحويل"

iface = gr.Interface(transcribe, "audio", "text")
iface.launch(share=True)
