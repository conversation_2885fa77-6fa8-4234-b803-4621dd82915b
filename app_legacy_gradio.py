#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة تستخدم Gradio قديم لتجنب مشاكل JSON Schema تماماً
Legacy Gradio version to completely avoid JSON Schema issues
"""

import gradio as gr
import whisper
import os
import warnings

# تجاهل التحذيرات
warnings.filterwarnings("ignore")

# API Key
API_KEY = "whisper-legacy-2025"

# متغير النموذج
model = None

def load_model():
    """تحميل النموذج"""
    global model
    if model is not None:
        return model
    
    try:
        # محاولة تحميل النموذج المحلي
        if os.path.exists("./models/small.pt"):
            model = whisper.load_model("./models/small.pt")
            print("✅ تم تحميل النموذج المحلي")
        else:
            # تحميل نموذج من الإنترنت
            model = whisper.load_model("base")
            print("✅ تم تحميل نموذج base")
        return model
    except Exception as e:
        print(f"خطأ: {e}")
        # محاولة أخيرة
        model = whisper.load_model("tiny")
        print("✅ تم تحميل نموذج tiny")
        return model

def transcribe(audio):
    """تحويل الصوت إلى نص"""
    if audio is None:
        return "❌ يرجى رفع ملف صوتي"
    
    try:
        whisper_model = load_model()
        result = whisper_model.transcribe(audio)
        text = result["text"].strip()
        
        if not text:
            return "❌ لم يتم العثور على نص"
        
        return f"{text}\n\n📊 عدد الكلمات: {len(text.split())}\n🔑 API: {API_KEY}"
        
    except Exception as e:
        return f"❌ خطأ: {str(e)}"

# إنشاء واجهة بسيطة جداً
print("🚀 بدء التطبيق مع Gradio قديم...")

# استخدام أبسط شكل من Interface
iface = gr.Interface(
    fn=transcribe,
    inputs="audio",  # استخدام string بدلاً من object
    outputs="text",  # استخدام string بدلاً من object
    title="محول الصوت إلى نص",
    description="ارفع ملف صوتي للحصول على النص"
)

print("✅ التطبيق جاهز")
print(f"🔑 API Key: {API_KEY}")

# تشغيل مع share=True
iface.launch(share=True)
