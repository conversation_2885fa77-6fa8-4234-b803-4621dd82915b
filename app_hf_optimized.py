#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة محسنة لـ Hugging Face Spaces - تحويل الصوت إلى نص
Optimized version for Hugging Face Spaces - Audio to Text Converter
"""

import gradio as gr
import whisper
import os
import secrets
from datetime import datetime
import warnings

# تجاهل التحذيرات
warnings.filterwarnings("ignore")

# إنشاء API Key فريد
API_KEY = f"whisper-small-{secrets.token_hex(8)}"

# متغير النموذج العام
model = None

def load_whisper_model():
    """تحميل نموذج Whisper مع تحقق ذكي"""
    global model
    
    if model is not None:
        print("✅ النموذج محمل بالفعل")
        return model
    
    print("🔄 تحميل النموذج...")
    
    # محاولة تحميل النموذج المحلي أولاً
    local_model_path = "./models/small.pt"
    
    if os.path.exists(local_model_path):
        try:
            print("📁 تحميل النموذج المحلي...")
            model = whisper.load_model(local_model_path)
            print("✅ تم تحميل النموذج المحلي")
            return model
        except Exception as e:
            print(f"⚠️ خطأ في النموذج المحلي: {e}")
    
    # تحميل نموذج من الإنترنت
    try:
        print("🌐 تحميل نموذج small...")
        model = whisper.load_model("small")
        print("✅ تم تحميل النموذج")
        return model
    except Exception as e:
        print(f"⚠️ خطأ في تحميل small: {e}")
        # محاولة نموذج أصغر
        model = whisper.load_model("base")
        print("✅ تم تحميل نموذج base")
        return model

def transcribe_audio(audio_file, language="Arabic"):
    """تحويل الصوت إلى نص"""
    if audio_file is None:
        return "❌ يرجى رفع ملف صوتي", "", ""
    
    try:
        # تحميل النموذج
        whisper_model = load_whisper_model()
        
        # تحديد اللغة
        lang_codes = {
            "تلقائي": None,
            "Arabic": "ar",
            "English": "en",
            "French": "fr",
            "Spanish": "es",
            "German": "de"
        }
        
        lang_code = lang_codes.get(language, "ar")
        
        # تحويل الصوت
        if lang_code is None:
            result = whisper_model.transcribe(audio_file)
        else:
            result = whisper_model.transcribe(audio_file, language=lang_code)
        
        # استخراج النتائج
        text = result.get("text", "").strip()
        detected_language = result.get("language", "غير محدد")
        
        # إحصائيات
        word_count = len(text.split()) if text else 0
        char_count = len(text) if text else 0
        
        # معلومات التحويل
        info = f"""📊 معلومات التحويل:
🤖 النموذج: Whisper Small
🌍 اللغة: {detected_language}
📝 الكلمات: {word_count}
🔤 الأحرف: {char_count}
🕒 الوقت: {datetime.now().strftime('%H:%M:%S')}"""

        # معلومات API
        api_info = f"""🔑 API Key: {API_KEY}

📡 استخدام API:
POST /api/predict
Content-Type: multipart/form-data"""
        
        print(f"✅ تم التحويل - {word_count} كلمة")
        return text, info, api_info
        
    except Exception as e:
        error_msg = f"❌ خطأ في التحويل: {str(e)}"
        print(error_msg)
        return error_msg, f"خطأ: {str(e)}", ""

# بدء التطبيق
print("🚀 بدء تطبيق Hugging Face Spaces...")
print(f"🔑 API Key: {API_KEY}")

# إنشاء واجهة Gradio محسنة
def create_interface():
    with gr.Blocks(
        title="محول الصوت إلى نص",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
            margin: auto !important;
        }
        """
    ) as demo:

        # العنوان
        gr.Markdown("""
        # 🎤 محول الصوت إلى نص
        ### مدعوم بنموذج Whisper Small - دقة عالية في التحويل
        """)

        # واجهة التحويل
        with gr.Row():
            with gr.Column(scale=1):
                audio_input = gr.Audio(
                    label="📁 ارفع ملف صوتي أو سجل صوتك", 
                    type="filepath",
                    sources=["upload", "microphone"]
                )
                language_choice = gr.Dropdown(
                    choices=["تلقائي", "Arabic", "English", "French", "Spanish", "German"],
                    value="Arabic",
                    label="🌍 اختر اللغة"
                )
                convert_btn = gr.Button("🚀 تحويل إلى نص", variant="primary", size="lg")

            with gr.Column(scale=2):
                output_text = gr.Textbox(
                    label="📝 النص المستخرج", 
                    lines=12, 
                    max_lines=20,
                    placeholder="سيظهر النص المحول هنا..."
                )

        with gr.Row():
            with gr.Column():
                info_output = gr.Textbox(
                    label="📊 معلومات التحويل", 
                    lines=6,
                    interactive=False
                )
            with gr.Column():
                api_output = gr.Textbox(
                    label="🔑 معلومات API", 
                    lines=6,
                    interactive=False
                )

        # معلومات إضافية
        with gr.Accordion("📖 معلومات الاستخدام", open=False):
            gr.Markdown(f"""
            ### 🔑 API Key للاستخدام البرمجي:
            ```
            {API_KEY}
            ```
            
            ### 📡 مثال على الاستخدام البرمجي:
            ```python
            import requests
            import json
            
            # رفع ملف صوتي
            with open("audio.mp3", "rb") as f:
                files = {{"data": f}}
                data = {{"data": [None, "Arabic"]}}
            
            response = requests.post("YOUR_SPACE_URL/api/predict", files=files, data=json.dumps(data))
            result = response.json()
            print(result["data"][0])  # النص المحول
            ```
            
            ### 🎯 الملفات المدعومة:
            - MP3, WAV, M4A, FLAC
            - حد أقصى: 25MB
            - مدة أقصى: 30 دقيقة
            """)

        # ربط الأحداث
        convert_btn.click(
            fn=transcribe_audio,
            inputs=[audio_input, language_choice],
            outputs=[output_text, info_output, api_output]
        )

    return demo

# إنشاء التطبيق
app = create_interface()

# تشغيل التطبيق - محسن لـ Hugging Face Spaces
print("✅ التطبيق جاهز")

# فحص النموذج المحلي
if os.path.exists("./models/small.pt"):
    size = os.path.getsize("./models/small.pt") / (1024 * 1024)
    print(f"📁 النموذج المحلي متوفر ({size:.1f}MB)")

# تشغيل مع share=True للعمل في Hugging Face Spaces
app.launch(share=True)
