#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للتأكد من الإصلاحات
Quick test for fixes
"""

import sys
import os

def test_app():
    """اختبار التطبيق"""
    print("🧪 اختبار التطبيق المحسن...")
    
    try:
        sys.path.insert(0, './huggingface_upload')
        import app
        
        print("✅ تم استيراد التطبيق بنجاح")
        
        # فحص المتغيرات
        if hasattr(app, 'API_KEY'):
            print(f"✅ API Key: {app.API_KEY}")
        
        if hasattr(app, 'app'):
            print("✅ تطبيق Gradio موجود")
        
        # اختبار دالة التحويل
        result = app.transcribe_audio(None, "Arabic")
        if isinstance(result, tuple) and len(result) == 3:
            print("✅ دالة التحويل تعمل")
        
        print("✅ جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    if test_app():
        print("\n🎉 التطبيق جاهز للرفع!")
        print("📁 المجلد: huggingface_upload/")
        print("🔧 تم حل جميع المشاكل")
    else:
        print("\n❌ يوجد مشاكل تحتاج إصلاح")
