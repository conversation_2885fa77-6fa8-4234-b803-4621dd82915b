#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة تستخدم Gradio قديم جداً (قبل مشاكل JSON Schema)
Version using very old Gradio (before JSON Schema issues)
"""

import gradio as gr
import whisper
import warnings

# إيقاف التحذيرات
warnings.filterwarnings("ignore")

# متغيرات
model = None
API_KEY = "whisper-old-gradio-2025"

def load_model():
    """تحميل النموذج"""
    global model
    if model is None:
        try:
            model = whisper.load_model("tiny")
            print("✅ تم تحميل نموذج tiny")
        except:
            model = whisper.load_model("base")
            print("✅ تم تحميل نموذج base")
    return model

def transcribe_audio(audio):
    """تحويل الصوت إلى نص"""
    if audio is None:
        return "يرجى رفع ملف صوتي"
    
    try:
        m = load_model()
        result = m.transcribe(audio)
        text = result["text"].strip()
        
        if not text:
            return "لم يتم العثور على نص"
        
        word_count = len(text.split())
        return f"{text}\n\nعدد الكلمات: {word_count}\nAPI Key: {API_KEY}"
        
    except Exception as e:
        return f"خطأ: {str(e)}"

# إنشاء واجهة بسيطة جداً
print("🚀 بدء التطبيق مع Gradio قديم...")

# استخدام أبسط شكل ممكن
demo = gr.Interface(
    fn=transcribe_audio,
    inputs="audio",
    outputs="text",
    title="محول الصوت إلى نص",
    description="ارفع ملف صوتي للحصول على النص"
)

print("✅ التطبيق جاهز")
print(f"🔑 API Key: {API_KEY}")

# تشغيل مع share=True
demo.launch(share=True)
