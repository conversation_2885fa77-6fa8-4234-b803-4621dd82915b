#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة خالية من الأخطاء - حل مشاكل JSON Schema و share=True
Error-free version - fixes JSON Schema and share=True issues
"""

import gradio as gr
import whisper
import os
import warnings
import sys

# تجاهل جميع التحذيرات
warnings.filterwarnings("ignore")

# API Key
API_KEY = "whisper-fixed-2025"

# متغير النموذج
model = None

def load_whisper_model():
    """تحميل النموذج بطريقة آمنة"""
    global model
    
    if model is not None:
        print("✅ النموذج محمل بالفعل")
        return model
    
    print("🔄 تحميل النموذج...")
    
    try:
        # محاولة تحميل النموذج المحلي أولاً
        local_path = "./models/small.pt"
        if os.path.exists(local_path):
            print("📁 تحميل النموذج المحلي...")
            model = whisper.load_model(local_path)
            print("✅ تم تحميل النموذج المحلي")
            return model
    except Exception as e:
        print(f"⚠️ خطأ في النموذج المحلي: {e}")
    
    try:
        # تحميل نموذج base (أكثر استقراراً من small)
        print("🌐 تحميل نموذج base...")
        model = whisper.load_model("base")
        print("✅ تم تحميل نموذج base")
        return model
    except Exception as e:
        print(f"⚠️ خطأ في تحميل base: {e}")
        # محاولة أخيرة مع tiny
        print("🌐 تحميل نموذج tiny...")
        model = whisper.load_model("tiny")
        print("✅ تم تحميل نموذج tiny")
        return model

def transcribe_audio(audio_file):
    """تحويل الصوت إلى نص - مبسط وآمن"""
    
    # التحقق من وجود الملف
    if audio_file is None:
        return "❌ يرجى رفع ملف صوتي"
    
    try:
        # تحميل النموذج
        whisper_model = load_whisper_model()
        
        print(f"🎤 معالجة الملف: {audio_file}")
        
        # تحويل الصوت مع معاملات آمنة
        result = whisper_model.transcribe(
            audio_file,
            language="ar",  # تحديد اللغة العربية مباشرة
            task="transcribe"
        )
        
        # استخراج النص
        text = result.get("text", "").strip()
        
        if not text:
            return "❌ لم يتم العثور على نص في الملف الصوتي"
        
        # إضافة معلومات بسيطة
        word_count = len(text.split())
        info = f"\n\n📊 عدد الكلمات: {word_count}\n🔑 API Key: {API_KEY}"
        
        print(f"✅ تم التحويل بنجاح - {word_count} كلمة")
        return text + info
        
    except Exception as e:
        error_msg = f"❌ خطأ في التحويل: {str(e)}"
        print(error_msg)
        return error_msg

# بدء التطبيق
print("🚀 بدء التطبيق الخالي من الأخطاء...")
print(f"🔑 API Key: {API_KEY}")

# إنشاء واجهة بسيطة جداً لتجنب مشاكل JSON Schema
def create_simple_interface():
    """إنشاء واجهة بسيطة تتجنب مشاكل Gradio"""
    
    # استخدام Interface بدلاً من Blocks لتجنب مشاكل JSON Schema
    interface = gr.Interface(
        fn=transcribe_audio,
        inputs=[
            gr.Audio(
                label="📁 ارفع ملف صوتي (MP3, WAV, M4A)",
                type="filepath"
            )
        ],
        outputs=[
            gr.Textbox(
                label="📝 النص المحول",
                lines=15,
                placeholder="سيظهر النص المحول هنا..."
            )
        ],
        title="🎤 محول الصوت إلى نص",
        description="""
        ### مدعوم بنموذج Whisper
        - ارفع ملف صوتي للحصول على النص
        - يدعم العربية والإنجليزية
        - حد أقصى: 25MB
        """,
        article=f"""
        ### 🔑 معلومات API:
        **API Key:** `{API_KEY}`
        
        ### 📡 استخدام برمجي:
        ```python
        import requests
        
        files = {{'data': open('audio.mp3', 'rb')}}
        response = requests.post('YOUR_SPACE_URL/api/predict', files=files)
        result = response.json()
        print(result['data'][0])
        ```
        """,
        examples=None,
        cache_examples=False,
        allow_flagging="never",
        analytics_enabled=False
    )
    
    return interface

# إنشاء التطبيق
app = create_simple_interface()

print("✅ التطبيق جاهز")

# فحص البيئة
if os.path.exists("./models/small.pt"):
    size = os.path.getsize("./models/small.pt") / (1024 * 1024)
    print(f"📁 النموذج المحلي متوفر ({size:.1f}MB)")

# تشغيل التطبيق - حل مشكلة share=True
print("🌐 تشغيل التطبيق مع share=True...")

try:
    # تشغيل مع إعدادات آمنة لـ Hugging Face Spaces
    app.launch(
        share=True,                    # حل مشكلة localhost
        server_name="0.0.0.0",        # قبول جميع الاتصالات
        server_port=7860,             # المنفذ الافتراضي
        show_error=True,              # إظهار الأخطاء
        quiet=False,                  # إظهار الرسائل
        enable_queue=False,           # تجنب مشاكل الطابور
        max_threads=10                # حد الخيوط
    )
except Exception as e:
    print(f"❌ خطأ في التشغيل: {e}")
    print("🔄 محاولة تشغيل بديلة...")
    
    # محاولة بديلة مع أقل معاملات
    try:
        app.launch(share=True)
    except Exception as e2:
        print(f"❌ فشل التشغيل البديل: {e2}")
        print("💡 تأكد من أن البيئة تدعم Gradio")
